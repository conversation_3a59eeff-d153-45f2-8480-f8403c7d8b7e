<template>
  <div v-if="dialogVisible" class="fullscreen-overlay">
    <div class="fullscreen-video-wrapper">
      <!-- 直接使用 VideoHeader 组件 -->
      <VideoHeader :message="message" :dubbingMode="dubbingMode" :action="action" @exitFullscreen="handleClose" />

      <div class="fullscreen-video-container">
        <video ref="fullscreenVideo" class="native-video-player" :src="videoSrc" crossOrigin="anonymous"
          controlsList="nodownload nofullscreen" disablePictureInPicture preload="metadata" controls :poster="poster"
          @play="handlePlay" @pause="handlePause" @ended="handleEnded"></video>
      </div>

      <!-- 直接使用 VideoBottom 组件 -->
      <VideoBottom :video="videoSrc" :poster="poster" :message="message" :show="true" :dubbingMode="dubbingMode"
        :action="action" @exitFullscreen="handleClose" @cutVideo="handleCutVideo" />

      <div class="bottom-box">
        <!-- 倒计时 -->
        <div v-if="countdownActive" class="countdown-container">
          <div class="countdown">{{ countdown }}</div>
        </div>

        <!-- 录制按钮（初始状态）-->
        <div class="record-btn-container" v-if="dubbingMode && !isRecording && !audioRecorded">
          <div class="record-btn" @click="startRecording">开始录制</div>
        </div>

        <!-- 录制中状态 -->
        <div class="recording-status" v-if="dubbingMode && isRecording">
          <div class="loading">
            <img :src="dubbingLoading" alt="" />
          </div>
          <div class="recording-actions">
            <div class="stop-recording-btn" @click="stopRecording">结束录制</div>
          </div>
        </div>

        <!-- 录制完成后的操作 -->
        <div class="preview-container" v-if="dubbingMode && audioRecorded">
          <div class="preview-actions">
            <div class="re-record-btn" @click="reRecord">重新录制</div>
            <div class="preview-btn" @click="previewDubbing">预览</div>
            <div class="merge-btn" @click="mergeDubbing" :class="{ disabled: uploadLoading || mergeLoading }">
              {{ uploadLoading ? '上传中...' : (mergeLoading ? '合成中...' : '合成') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'FullscreenVideo',
}
</script>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
// 导入新的组件
import VideoHeader from '../VideoPlayer/VideoHeader.vue'
import VideoBottom from '../VideoPlayer/VideoBottom.vue'
import useChatStore from '@/store/modules/chat'
import { audioPlayStatusJSBridge, saveMediaViaJSBridge } from '@/utils/messageUtils'
import { ElMessage } from 'element-plus'
import { ossClient } from '../TUIKit/utils/tool'
import { sendCustomMessage } from '@/utils/customIM'
import { createRecordAppRecorder, RecordAppRecorder } from '@/utils/recorderCore'
import dubbingLoading from '@/assets/images/ChatAiTool/dubbing-loading.gif'
import { jsbridge } from 'msb-public-library'

const chatStore = useChatStore()

const dialogVisible = ref(false)
const fullscreenVideo = ref<HTMLVideoElement | null>(null)
let originalOverflow = ''

// 从store获取视频相关属性
const videoSrc = ref('')
const poster = ref('')
const message = ref({})
const dubbingMode = ref(false)
const videoEndedCallbacks = ref<Array<() => void>>([])

// 添加录音相关的状态变量
const isRecording = ref(false)
const recordingTime = ref(0)
const recordTimer = ref<number | null>(null)
const countdownActive = ref(false)
const countdown = ref(3)
const countdownTimer = ref<number | null>(null)
const audioRecorded = ref(false)
const recordedAudioUrl = ref('')
const recordedAudioBlob = ref<Blob | null>(null) // 新增：本地音频blob
const audioUploaded = ref(false) // 新增：音频是否已上传
const uploadLoading = ref(false) // 新增：音频上传中状态
const isPlaying = ref(false)
const recorder = ref<RecordAppRecorder | null>(null)
const hasPermission = ref(false)
const originalVideoVolume = ref(1)
const mergeLoading = ref(false)
let previewAudio: HTMLAudioElement | null = null

// action 函数处理各种操作
const action = (type: string, data: any) => {
  console.log('action', type)
  switch (type) {
    case 'music':
      if (data) {
        fullscreenVideo.value?.pause()
        chatStore.setFullscreenVideoPlaying(false)
      }
      break
    case 'dubbing':
      dubbingMode.value = data
      if (data) {
        // 进入AI配音模式
        fullscreenVideo.value?.pause()
        chatStore.setFullscreenVideoPlaying(false)
      } else {
        // 退出AI配音模式
        exitDubbingMode()
      }
      break
    case 'merge':
      // 播放器退出全屏
      if (document.fullscreenElement && fullscreenVideo.value) {
        document.exitFullscreen()
      }
      fullscreenVideo.value?.pause()
      chatStore.setFullscreenVideoPlaying(false)
      handleClose()
      break
    default:
      break
  }
}

// 视频控制方法
const resetVideo = () => {
  if (fullscreenVideo.value) {
    fullscreenVideo.value.currentTime = 0
    chatStore.setFullscreenVideoPlaying(false)
  }
}

const playVideo = () => {
  if (fullscreenVideo.value) {
    fullscreenVideo.value.play()
    chatStore.setFullscreenVideoPlaying(true)
  }
}

const pauseVideo = () => {
  if (fullscreenVideo.value) {
    fullscreenVideo.value.pause()
    chatStore.setFullscreenVideoPlaying(false)
  }
}

const getVideoVolume = (): number => {
  return fullscreenVideo.value?.volume || 1
}

const setVideoVolume = (volume: number) => {
  if (fullscreenVideo.value) {
    fullscreenVideo.value.volume = volume
    if (volume === 0) {
      fullscreenVideo.value.muted = true
    } else {
      fullscreenVideo.value.muted = false
    }
  }
}

// 视频结束事件处理
const onVideoEnded = (callback: () => void) => {
  videoEndedCallbacks.value.push(callback)
}

const offVideoEnded = (callback: () => void) => {
  videoEndedCallbacks.value = videoEndedCallbacks.value.filter(cb => cb !== callback)
}

const _handleCutVideo = async () => {
  console.log('🚀 ~ handleCutVideo ~ handleCutVideo:')
  const videoDom = fullscreenVideo.value
  if (!videoDom) {
    return
  }

  console.log('🚀 ~ handleCutVideo ~ videoDom.currentTime:', videoDom.currentTime)
  if (videoDom.currentTime === 0) {
    ElMessage.error('视频封面不支持截取')
    return
  }
  try {
    let blob: Blob | null = null

    // 创建canvas来截取视频帧
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      throw new Error('无法创建canvas上下文')
    }

    canvas.width = videoDom.videoWidth
    canvas.height = videoDom.videoHeight

    // 绘制当前视频帧到canvas
    ctx.drawImage(videoDom, 0, 0, canvas.width, canvas.height)

    // 将canvas转换为blob
    blob = await new Promise<Blob>((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob)
        } else {
          reject(new Error('截图失败'))
        }
      }, 'image/jpeg', 0.8)
    })

    if (!blob) {
      throw new Error('截图生成失败')
    }

    // 转换为File对象，让OSS客户端自动生成文件名
    const fileName = `screenshot_${Date.now()}.jpg`
    const file = new File([blob], fileName, { type: 'image/jpeg' })

    // 上传到OSS（不传第二个参数，让OSS客户端自动处理文件名）
    const url = await ossClient().upload(file)

    if (!url) {
      throw new Error('图片上传失败')
    }

    console.log('截图成功:', url)
    // 构造一个简单的消息对象，用于保存到本地相册
    const screenshotMessage = {
      payload: {
        data: JSON.stringify({
          images: [url],
          statusMsgData: {
            type: 'image',
          },
        }),
      },
      from: 'agent',
    }

    // 调用saveMediaViaJSBridge保存截图到本地相册
    saveMediaViaJSBridge(screenshotMessage, () => {
      // ElMessage.success('截图已保存')
      handleClose()
    })

    return url
  } catch (error) {
    console.error('截图处理失败:', error)
    // 如果是权限错误，提示用户重试
    ElMessage.error('截图失败，请重试')
    return null
  }
}

const handleCutVideo = _handleCutVideo

// 监听store中的全屏状态
watch(
  () => chatStore.fullscreen,
  newValue => {
    dialogVisible.value = newValue
    dubbingMode.value = false
    console.log('🚀 ~ dialogVisible.value:', dialogVisible.value)
    if (newValue) {
      disableInteractions()
      if (fullscreenVideo.value) {
        fullscreenVideo.value.currentTime = 0
      }
      audioPlayStatusJSBridge('play')
    } else {
      enableInteractions()
      audioPlayStatusJSBridge('stop')
    }
  }
)

// 监听store中的视频源变化
watch(
  () => chatStore.videoSrc,
  newValue => {
    videoSrc.value = newValue
  }
)

// 监听store中的海报变化
watch(
  () => chatStore.videoPoster,
  newValue => {
    poster.value = newValue
  }
)

// 监听store中的消息变化
watch(
  () => chatStore.videoMessage,
  newValue => {
    message.value = newValue
  }
)

const lockScroll = () => {
  originalOverflow = document.body.style.overflow
  document.body.style.overflow = 'hidden'
}

const unlockScroll = () => {
  document.body.style.overflow = originalOverflow
}

// 在打开全屏时禁用所有交互
const disableInteractions = () => {
  document.body.style.overflow = 'hidden'
  document.body.style.touchAction = 'none'
  document.body.style.userSelect = 'none'
  document.body.style.webkitUserSelect = 'none'
  document.body.style.pointerEvents = 'none'
}

// 在关闭全屏时恢复交互
const enableInteractions = () => {
  document.body.style.overflow = originalOverflow
  document.body.style.touchAction = ''
  document.body.style.userSelect = ''
  document.body.style.webkitUserSelect = ''
  document.body.style.pointerEvents = ''
}

const _handleClose = () => {
  console.log('handleClose')
  chatStore.setFullscreen(false)
  enableInteractions()
  if (fullscreenVideo.value) {
    fullscreenVideo.value.pause()
    chatStore.setFullscreenVideoPlaying(false)
  }
}

const handleClose = _handleClose

// 录音相关方法（保持原有逻辑）
const initRecorder = () => {
  if (recorder.value) {
    recorder.value.destroy()
  }
  recorder.value = createRecordAppRecorder({
    sampleRate: 16000,
    numChannels: 1,
    encoderPath: '/js/encoderWorker.min.js',
  })
}

const startRecording = () => {
  requestRecordingPermission()
}

const requestRecordingPermission = () => {
  if (hasPermission.value) {
    startRecordingWithPermission()
    return
  }

  nextTick(() => {
    console.log('开始请求麦克风权限...')

    try {
      jsbridge?.sendMsg({
        action: 'microphoneStatus',
        params: {
          status: 'open',
        },
      })
    } catch (e) {
      console.warn('JSBridge调用失败，可能不在原生环境中', e)
    }

    let retryCount = 0
    const maxRetries = 2

    const requestPermission = async () => {
      if (!recorder.value) {
        console.error('录音器未初始化，重新初始化')
        initRecorder()
        if (!recorder.value) {
          ElMessage.error('录音器初始化失败')
          resetRecordingState()
          return
        }
      }

      try {
        console.log(`尝试请求录音权限 (${retryCount + 1}/${maxRetries + 1})`)
        await recorder.value.requestPermission()
        hasPermission.value = true
        console.log('录音权限获取成功')
        startRecordingWithPermission()
      } catch (error: any) {
        console.error('录音权限请求失败:', error)
        retryCount++

        if (retryCount <= maxRetries) {
          console.log(`权限请求失败，${1000}ms后重试...`)
          setTimeout(requestPermission, 1000)
        } else {
          ElMessage.error('无法获取录音权限，请检查麦克风设置')
          resetRecordingState()
        }
      }
    }

    requestPermission()
  })
}

const startRecordingWithPermission = () => {
  console.log('开始倒计时...')
  countdownActive.value = true
  countdown.value = 3

  countdownTimer.value = window.setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
        countdownTimer.value = null
      }
      countdownActive.value = false
      isRecording.value = true
      startActualRecording()
    }
  }, 1000)
}

const startActualRecording = async () => {
  try {
    console.log('开始准备录音...')

    originalVideoVolume.value = getVideoVolume()
    setVideoVolume(0)
    resetVideo()

    nextTick(async () => {
      try {
        playVideo()

        if (!recorder.value) {
          throw new Error('录音器已失效')
        }

        console.log('开始录音...')
        await recorder.value.start()
        recordingTime.value = 0

        if (recordTimer.value) {
          clearInterval(recordTimer.value)
        }

        recordTimer.value = window.setInterval(() => {
          recordingTime.value++
        }, 1000)

        onVideoEnded(_stopRecording)

        console.log('录音已成功开始')
      } catch (error) {
        console.error('开始录音失败:', error)
        ElMessage.error('开始录音失败，请重试')
        pauseVideo()
        resetRecordingState()
      }
    })
  } catch (error: any) {
    console.error('录音失败:', error)
    ElMessage.error('录音失败')
    isRecording.value = false
    audioRecorded.value = false

    if (recordTimer.value) {
      clearInterval(recordTimer.value)
      recordTimer.value = null
    }

    if (recorder.value) {
      recorder.value.destroy()
      recorder.value = null
      hasPermission.value = false
    }

    try {
      jsbridge?.sendMsg({
        action: 'microphoneStatus',
        params: {
          status: 'close',
        },
      })
    } catch (e) {
      console.warn('JSBridge调用失败，可能不在原生环境中', e)
    }

    resetRecordingState()
  }
}

const _stopRecording = async () => {
  console.log('停止录音...')

  if (!isRecording.value || !recorder.value) {
    console.log('当前没有在录音或录音器不存在')
    return
  }

  try {
    isRecording.value = false
    pauseVideo()
    offVideoEnded(_stopRecording)

    if (recordTimer.value) {
      clearInterval(recordTimer.value)
      recordTimer.value = null
    }

    console.log('正在停止录音器...')
    const audioBlob = await recorder.value.stop()

    // 修复类型检查：audioBlob 是包含 blob 属性的对象
    if (!audioBlob || !audioBlob.blob || audioBlob.blob.size === 0) {
      throw new Error('录音数据为空')
    }

    // 保存本地音频blob，不立即上传
    recordedAudioBlob.value = audioBlob.blob

    // 创建本地URL用于预览
    recordedAudioUrl.value = URL.createObjectURL(audioBlob.blob)
    audioRecorded.value = true
    audioUploaded.value = false // 标记为未上传

    console.log('录音完成，已保存到本地')
    ElMessage.success('录音完成')

    try {
      jsbridge?.sendMsg({
        action: 'microphoneStatus',
        params: {
          status: 'close',
        },
      })
    } catch (e) {
      console.warn('JSBridge调用失败，可能不在原生环境中', e)
    }
  } catch (error: any) {
    console.error('录音失败:', error)
    ElMessage.error('录音失败')
    isRecording.value = false
    audioRecorded.value = false

    if (recordTimer.value) {
      clearInterval(recordTimer.value)
      recordTimer.value = null
    }

    if (recorder.value) {
      recorder.value.destroy()
      recorder.value = null
      hasPermission.value = false
    }

    try {
      jsbridge?.sendMsg({
        action: 'microphoneStatus',
        params: {
          status: 'close',
        },
      })
    } catch (e) {
      console.warn('JSBridge调用失败，可能不在原生环境中', e)
    }

    resetRecordingState()
  }
}

const stopRecording = _stopRecording

const resetRecordingState = () => {
  console.log('重置录音状态...')
  isRecording.value = false
  recordingTime.value = 0
  countdownActive.value = false
  countdown.value = 3
  audioRecorded.value = false
  audioUploaded.value = false
  uploadLoading.value = false
  mergeLoading.value = false
  isPlaying.value = false

  // 清理本地音频blob和URL
  if (recordedAudioBlob.value) {
    recordedAudioBlob.value = null
  }

  if (recordedAudioUrl.value) {
    // 如果是本地URL，需要释放
    if (recordedAudioUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(recordedAudioUrl.value)
    }
    recordedAudioUrl.value = ''
  }

  setVideoVolume(originalVideoVolume.value)

  if (recordTimer.value) {
    clearInterval(recordTimer.value)
    recordTimer.value = null
  }

  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }

  if (previewAudio) {
    try {
      previewAudio.pause()
      previewAudio.src = ''
      previewAudio = null
    } catch (e) {
      console.error('清理预览音频失败:', e)
    }
  }

  if (recorder.value) {
    try {
      recorder.value.destroy()
      console.log('录音器已关闭')

      try {
        jsbridge?.sendMsg({
          action: 'microphoneStatus',
          params: {
            status: 'close',
          },
        })
      } catch (e) {
        console.warn('JSBridge调用失败，可能不在原生环境中', e)
      }
    } catch (e) {
      console.error('关闭录音器失败:', e)
    } finally {
      recorder.value = null
      hasPermission.value = false
    }
  }

  console.log('录音状态已重置，可以重新开始录制')
}

const exitDubbingMode = () => {
  console.log('退出AI配音模式...')
  dubbingMode.value = false
  isRecording.value = false
  recordingTime.value = 0
  countdownActive.value = false
  countdown.value = 3
  audioRecorded.value = false
  audioUploaded.value = false
  uploadLoading.value = false
  mergeLoading.value = false
  isPlaying.value = false

  // 清理本地音频blob和URL
  if (recordedAudioBlob.value) {
    recordedAudioBlob.value = null
  }

  if (recordedAudioUrl.value) {
    // 如果是本地URL，需要释放
    if (recordedAudioUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(recordedAudioUrl.value)
    }
    recordedAudioUrl.value = ''
  }

  setVideoVolume(originalVideoVolume.value)

  if (recordTimer.value) {
    clearInterval(recordTimer.value)
    recordTimer.value = null
  }

  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }

  if (previewAudio) {
    try {
      previewAudio.pause()
      previewAudio.src = ''
      previewAudio = null
    } catch (e) {
      console.error('清理预览音频失败:', e)
    }
  }

  if (recorder.value) {
    try {
      recorder.value.destroy()
      console.log('退出配音模式，录音器已关闭')

      try {
        jsbridge?.sendMsg({
          action: 'microphoneStatus',
          params: {
            status: 'close',
          },
        })
      } catch (e) {
        console.warn('JSBridge调用失败，可能不在原生环境中', e)
      }
    } catch (e) {
      console.error('关闭录音器失败:', e)
    } finally {
      recorder.value = null
      hasPermission.value = false
    }
  }

  console.log('AI配音模式已完全退出')
}

const sleep = (time = 1000) => {
  return new Promise(resolve => {
    setTimeout(resolve, time)
  })
}

const _previewDubbing = () => {
  if (!recordedAudioUrl.value) {
    ElMessage.error('没有录制的音频')
    return
  }

  if (isPlaying.value) {
    if (previewAudio) {
      previewAudio.pause()
      previewAudio = null
    }
    pauseVideo()
    isPlaying.value = false
    return
  }

  if (fullscreenVideo.value) {
    try {
      previewAudio = new Audio(recordedAudioUrl.value)
      previewAudio.addEventListener('canplay', () => {
        if (previewAudio) {
          previewAudio.volume = 1.0
          previewAudio.muted = false
        }
      })

      previewAudio.onerror = e => {
        console.error('音频播放失败:', e)
        isPlaying.value = false
        pauseVideo()
      }

      console.log('开始播放录制的音频:', recordedAudioUrl.value)
      previewAudio.play()
      playVideo()
      isPlaying.value = true

      previewAudio.onended = () => {
        console.log('音频播放结束')
        isPlaying.value = false
        previewAudio = null
      }
    } catch (error) {
      console.error('预览配音时发生错误:', error)
      ElMessage.error('预览失败，请重试')
      isPlaying.value = false
    }
  }
}

const previewDubbing = _previewDubbing

const _reRecord = () => {
  console.log('重新录制...')
  audioRecorded.value = false
  audioUploaded.value = false
  uploadLoading.value = false
  isPlaying.value = false

  // 清理本地音频blob和URL
  if (recordedAudioBlob.value) {
    recordedAudioBlob.value = null
  }

  if (recordedAudioUrl.value) {
    // 如果是本地URL，需要释放
    if (recordedAudioUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(recordedAudioUrl.value)
    }
    recordedAudioUrl.value = ''
  }

  if (previewAudio) {
    try {
      previewAudio.pause()
      previewAudio.src = ''
      previewAudio = null
    } catch (e) {
      console.error('清理预览音频失败:', e)
    }
  }

  pauseVideo()
  console.log('准备重新录制')
}

const reRecord = _reRecord

const _mergeDubbing = async () => {
  if (!recordedAudioBlob.value) {
    ElMessage.error('没有录制的音频')
    return
  }

  try {
    // 如果音频还未上传，先上传
    if (!audioUploaded.value) {
      uploadLoading.value = true

      try {
        const fileName = `dubbing_${Date.now()}.wav`
        console.log('开始上传录音文件:', fileName)

        const uploadResult = await ossClient().upload(recordedAudioBlob.value)

        if (!uploadResult) {
          throw new Error('录音上传失败，未获取到URL')
        }

        // 更新为上传后的URL
        recordedAudioUrl.value = uploadResult
        audioUploaded.value = true

        console.log('录音上传成功:', uploadResult)
      } catch (error) {
        console.error('音频上传失败:', error)
        ElMessage.error('音频上传失败，请重试')
        return
      } finally {
        uploadLoading.value = false
      }
    }

    // 开始合成
    mergeLoading.value = true
    await sleep(600)

    const payload = {
      data: {
        businessID: 'say_hidden_message',
        content: {
          name: 'cmd_msg',
          data: {
            text: '',
            referenceList: [
              {
                type: 'video',
                data: videoSrc.value,
              },
              {
                type: 'audio',
                data: recordedAudioUrl.value,
              },
            ],
            cmd: '/视频配音乐非静音',
          },
        },
      },
    }

    await sendCustomMessage(payload)
    action('merge', null)
    exitDubbingMode()
    ElMessage.success('视频配音合成成功')
  } catch (error) {
    console.error('合成失败:', error)
    ElMessage.error('视频配音合成失败')
  } finally {
    mergeLoading.value = false
  }
}

const mergeDubbing = _mergeDubbing

// 原生视频播放事件处理
const handlePlay = () => {
  chatStore.setFullscreenVideoPlaying(true)
}

const handlePause = () => {
  chatStore.setFullscreenVideoPlaying(false)
}

const handleEnded = () => {
  videoEndedCallbacks.value.forEach(callback => callback())
}

onMounted(() => {
  nextTick(() => {
    if (fullscreenVideo.value) {
      customizeVideoControls()
    }
  })

  const activateAudioContext = () => {
    try {
      const tempContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      tempContext.resume().then(() => {
        nextTick(() => {
          tempContext.close()
          console.log('组件挂载时：音频上下文已预先激活')
        })
      })
    } catch (e) {
      console.warn('组件挂载时：预激活音频上下文失败，但这不影响后续操作', e)
    }
  }

  activateAudioContext()

  const userInteractionHandler = () => {
    activateAudioContext()
    document.removeEventListener('click', userInteractionHandler)
    document.removeEventListener('touchstart', userInteractionHandler)
  }

  document.addEventListener('click', userInteractionHandler)
  document.addEventListener('touchstart', userInteractionHandler)
})

const customizeVideoControls = () => {
  if (fullscreenVideo.value) {
    fullscreenVideo.value.onloadedmetadata = () => {
      applyControlsStyles()
    }
  }
}

const applyControlsStyles = () => {
  nextTick(() => {
    document.head.insertAdjacentHTML(
      'beforeend',
      `
      <style>
        video::-webkit-media-controls-fullscreen-button {
          display: none !important;
        }
        
        video::-webkit-media-controls-overflow-button,
        video::-webkit-media-controls-more-button {
          display: none !important;
        }
        
        video::-webkit-media-controls-volume-slider,
        video::-webkit-media-controls-mute-button {
          display: inline-block !important;
        }
        
        video::-webkit-media-controls-toggle-closed-captions-button,
        video::-webkit-media-controls-picture-in-picture-button {
          display: none !important;
        }
      </style>
      `
    )
  })
}

onBeforeUnmount(() => {
  enableInteractions()
  videoEndedCallbacks.value = []

  // 清理本地音频blob和URL
  if (recordedAudioBlob.value) {
    recordedAudioBlob.value = null
  }

  if (recordedAudioUrl.value && recordedAudioUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(recordedAudioUrl.value)
  }

  if (previewAudio) {
    try {
      previewAudio.pause()
      previewAudio.src = ''
      previewAudio = null
    } catch (e) {
      console.error('组件卸载：清理预览音频失败', e)
    }
  }

  if (isRecording.value && recorder.value) {
    try {
      recorder.value.stop().catch((e: any) => {
        console.error('组件卸载：停止录音失败', e)
      })
    } catch (e) {
      console.error('组件卸载：停止录音失败', e)
    }
  }

  if (recorder.value) {
    try {
      recorder.value.destroy()
      console.log('组件卸载：录音器已关闭')

      try {
        jsbridge?.sendMsg({
          action: 'microphoneStatus',
          params: {
            status: 'close',
          },
        })
      } catch (e) {
        console.warn('组件卸载：JSBridge调用失败，可能不在原生环境中', e)
      }
    } catch (e) {
      console.error('组件卸载：关闭录音器失败', e)
    }
    recorder.value = null
  }

  if (recordTimer.value) {
    clearInterval(recordTimer.value)
    recordTimer.value = null
  }

  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }

  hasPermission.value = false
})
</script>

<style lang="scss" scoped>
.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  pointer-events: auto !important;
  isolation: isolate;

  /* * {
    touch-action: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: auto !important;
  } */
}

.fullscreen-video-wrapper {
  background: black;
  position: relative;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  flex-direction: column;
  display: flex;
}

.fullscreen-video-container {
  // 占据剩余空间
  flex: 1;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: rgba(49, 49, 49, 1);

  .native-video-player {
    width: 100%;
    height: 100%;
    object-fit: contain;
    pointer-events: auto;
  }
}

.bottom-box {
  width: 100%;

  .record-btn-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 30px 0;

    .record-btn {
      width: 370px;
      height: 63px;
      background: #086df7;
      border-radius: 31px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 63px;
      text-align: center;
    }

    .btn-box {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      width: 100%;

      .btn-box-left {
        width: 219px;
        height: 63px;
        background: #687086;
        border-radius: 31px;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 22px;
        color: #ffffff;
        text-align: center;
        line-height: 63px;
      }

      .btn-box-center {
        width: 219px;
        height: 63px;
        background: #67c23a;
        border-radius: 31px;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 22px;
        color: #ffffff;
        text-align: center;
        line-height: 63px;
      }

      .btn-box-right {
        width: 219px;
        height: 63px;
        background: #086df7;
        border-radius: 31px;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 22px;
        color: #ffffff;
        text-align: center;
        line-height: 63px;
      }
    }
  }
}

:deep(.native-video-player::-webkit-media-controls-fullscreen-button) {
  display: none !important;
}

:deep(.native-video-player::-webkit-media-controls-overflow-button),
:deep(.native-video-player::-webkit-media-controls-more-button) {
  display: none !important;
}

:deep(.native-video-player::-webkit-media-controls-picture-in-picture-button) {
  display: none !important;
}

.close-btn-dub {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 2200;

  img {
    width: 22px;
    height: 20px;
  }
}

// 倒计时容器样式
.countdown-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 2100;

  .countdown {
    font-size: 80px;
    font-weight: 500;
    color: #ffffff;
    animation: pulse 1s infinite;
    width: 105px;
    height: 105px;
    background: #000000;
    border-radius: 15px;
    opacity: 0.45;
    text-align: center;
    line-height: 105px;
  }

  .countdown-text {
    font-size: 24px;
    color: #ffffff;
    margin-top: 20px;
  }
}

// 录音状态样式
.recording-status {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  margin: 30px 0;

  .loading {
    width: 284px;
    height: 42px;
    margin-bottom: 42px;

    img {
      width: 100%;
      object-fit: contain;
    }
  }

  .recording-indicator {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .recording-wave {
      width: 16px;
      height: 16px;
      background-color: #f56c6c;
      border-radius: 50%;
      margin-right: 10px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #f56c6c;
        border-radius: 50%;
        animation: pulse 1.5s infinite;
      }
    }

    span {
      font-size: 14px;
      color: #ffffff;
    }
  }

  .recording-actions {
    .stop-recording-btn {
      width: 370px;
      height: 63px;
      background: #086df7;
      border-radius: 31px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      line-height: 63px;
    }
  }
}

// 预览容器样式
.preview-container {
  width: 100%;
  margin: 30px 0;

  .preview-actions {
    display: flex;
    justify-content: space-around;
    align-items: center;

    .re-record-btn {
      width: 219px;
      height: 63px;
      background: #687086;
      border-radius: 31px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      line-height: 63px;
    }

    .preview-btn {
      width: 219px;
      height: 63px;
      background: #67c23a;
      border-radius: 31px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      line-height: 63px;
    }

    .merge-btn {
      width: 220px;
      height: 63px;
      background: #086df7;
      border-radius: 31px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      line-height: 63px;
    }

    .preview-btn {
      background: #409eff;
      color: #ffffff;

      &:hover {
        background: #66b1ff;
      }
    }

    .re-record-btn {
      background: #909399;
      color: #ffffff;

      &:hover {
        background: #a6a9ad;
      }
    }

    .merge-btn {
      background: #67c23a;
      color: #ffffff;

      &:hover {
        background: #85ce61;
      }

      &.disabled {
        background: #9ba0ac;
        cursor: not-allowed;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
